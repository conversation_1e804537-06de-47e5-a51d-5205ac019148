"""
Configuration file for Map Capture Application

Modify these settings to match your screen setup and preferences.
"""

# Default capture regions for common screen resolutions
CAPTURE_REGIONS = {
    "1920x1080": (0, 100, 1920, 980),    # Full HD, excluding browser bars
    "1366x768": (0, 100, 1366, 668),     # Common laptop resolution
    "2560x1440": (0, 120, 2560, 1320),   # 1440p, excluding browser bars
    "3840x2160": (0, 150, 3840, 2010),   # 4K, excluding browser bars
    "1280x720": (0, 90, 1280, 630),      # HD, excluding browser bars
    "custom": (0, 100, 1920, 880)        # Custom region - modify as needed
}

# Default grid settings
DEFAULT_ROWS = 3
DEFAULT_COLS = 3
MAX_GRID_SIZE = 10

# Timing settings (in seconds)
COUNTDOWN_TIME = 5          # Countdown before starting capture
SCROLL_WAIT_TIME = 2        # Wait time after scrolling for map to load
HORIZONTAL_RESET_WAIT = 1   # Wait time during horizontal position reset

# Scroll settings
SCROLL_OVERLAP_FACTOR = 0.95  # How much to scroll (0.95 = 95% of capture area)
SCROLL_DURATION = 0.5         # Duration of scroll animation in seconds

# Image settings
SCREENSHOT_FORMAT = "PNG"     # Format for individual screenshots
FINAL_IMAGE_FORMAT = "PNG"    # Format for final stitched image
FINAL_IMAGE_QUALITY = 95      # Quality for JPEG (if using JPEG format)

# Directory settings
SCREENSHOTS_DIR = "screenshots"
FINAL_IMAGE_NAME = "stitched_map.png"

# Web automation settings
WEB_AUTOMATION_WAIT_TIME = 20    # Maximum wait time for web elements
BROWSER_STARTUP_WAIT = 5         # Wait time for browser to fully start
MAP_LOAD_WAIT = 10              # Wait time for map to load completely
PAN_OVERLAP_FACTOR = 0.8        # Overlap factor for map panning (80% = 20% overlap)

# GUI settings
WINDOW_TITLE = "Web Map Capture & Stitching Tool"
WINDOW_SIZE = "500x600"
KEEP_ON_TOP = True           # Keep application window on top

# Map service settings
DEFAULT_MAP_URL = "https://hsac.org.in/eodb/"
SUPPORTED_MAP_SERVICES = {
    "hsac": {
        "url": "https://hsac.org.in/eodb/",
        "name": "HSAC Land Records",
        "wait_time": 5,
        "pan_method": "drag"
    },
    "google_maps": {
        "url": "https://maps.google.com",
        "name": "Google Maps",
        "wait_time": 3,
        "pan_method": "drag"
    }
}

# Browser-specific capture regions
# Adjust these based on your browser and its toolbar configuration
BROWSER_REGIONS = {
    "chrome_fullscreen": (0, 0, 1920, 1080),
    "chrome_windowed": (0, 100, 1920, 980),
    "firefox_fullscreen": (0, 0, 1920, 1080),
    "firefox_windowed": (0, 110, 1920, 970),
    "edge_windowed": (0, 105, 1920, 975),
    "safari_windowed": (0, 95, 1920, 985)  # macOS
}

# Map service specific settings
MAP_SERVICES = {
    "google_maps": {
        "recommended_region": (0, 100, 1920, 980),
        "scroll_wait_time": 2,
        "notes": "Works best in full-screen mode"
    },
    "openstreetmap": {
        "recommended_region": (0, 80, 1920, 1000),
        "scroll_wait_time": 1.5,
        "notes": "Usually loads faster than Google Maps"
    },
    "bing_maps": {
        "recommended_region": (0, 120, 1920, 960),
        "scroll_wait_time": 2.5,
        "notes": "May need longer wait times"
    }
}

def get_capture_region_for_resolution(width, height):
    """
    Get the recommended capture region for a given screen resolution.
    
    Args:
        width (int): Screen width in pixels
        height (int): Screen height in pixels
    
    Returns:
        tuple: (x1, y1, x2, y2) capture region coordinates
    """
    resolution_key = f"{width}x{height}"
    
    if resolution_key in CAPTURE_REGIONS:
        return CAPTURE_REGIONS[resolution_key]
    
    # Calculate a reasonable default for unknown resolutions
    # Exclude top 100px for browser toolbar and bottom 100px for taskbar
    return (0, 100, width, height - 100)

def get_recommended_grid_size(capture_width, capture_height, target_resolution=2000):
    """
    Calculate recommended grid size based on capture area and target resolution.
    
    Args:
        capture_width (int): Width of capture region
        capture_height (int): Height of capture region
        target_resolution (int): Target pixel count for final image width/height
    
    Returns:
        tuple: (recommended_rows, recommended_cols)
    """
    # Calculate how many captures needed to reach target resolution
    cols = max(1, min(MAX_GRID_SIZE, target_resolution // capture_width))
    rows = max(1, min(MAX_GRID_SIZE, target_resolution // capture_height))
    
    return rows, cols
