#!/usr/bin/env python3
"""
Simple launcher for the web automation map capture app

This script handles import issues and provides better error reporting.
"""

import sys
import os

def check_dependencies():
    """Check if all required dependencies are available"""
    print("Checking dependencies...")
    
    missing = []
    
    try:
        import tkinter
        print("✓ Tkinter available")
    except ImportError:
        missing.append("tkinter")
        print("✗ Tkinter missing")
    
    try:
        import selenium
        print(f"✓ Selenium available (version {selenium.__version__})")
    except ImportError:
        missing.append("selenium")
        print("✗ Selenium missing")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ WebDriver Manager available")
    except ImportError:
        missing.append("webdriver-manager")
        print("✗ WebDriver Manager missing")
    
    try:
        from PIL import Image
        print("✓ Pillow available")
    except ImportError:
        missing.append("pillow")
        print("✗ Pillow missing")
    
    try:
        import requests
        print("✓ Requests available")
    except ImportError:
        missing.append("requests")
        print("✗ Requests missing")
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Please install them with:")
        print("pip install selenium webdriver-manager pillow requests")
        return False
    
    print("\n✓ All dependencies available!")
    return True

def run_application():
    """Run the web automation application"""
    try:
        # Import the application
        from web_map_capture_app import WebMapCaptureApp
        import tkinter as tk
        
        print("Starting Web Map Capture Application...")
        
        # Create and run the application
        root = tk.Tk()
        app = WebMapCaptureApp(root)
        
        print("Application window created. Starting GUI...")
        root.mainloop()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("\nTrying alternative import method...")
        
        # Try running as subprocess
        import subprocess
        try:
            result = subprocess.run([sys.executable, "web_map_capture_app.py"], 
                                  cwd=os.getcwd(), 
                                  capture_output=False)
            return result.returncode == 0
        except Exception as e2:
            print(f"Subprocess execution failed: {e2}")
            return False
            
    except Exception as e:
        print(f"Application error: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("Web Map Capture Application Launcher")
    print("=" * 40)
    
    # Check dependencies first
    if not check_dependencies():
        print("\nPlease install missing dependencies and try again.")
        input("Press Enter to exit...")
        return
    
    print("\nStarting application...")
    
    # Run the application
    success = run_application()
    
    if not success:
        print("\nApplication failed to start.")
        print("\nTroubleshooting tips:")
        print("1. Restart your IDE/editor")
        print("2. Try running directly: python web_map_capture_app.py")
        print("3. Check Python interpreter path")
        print("4. Reinstall dependencies: pip install -r requirements.txt")
        
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
