#!/usr/bin/env python3
"""
Simple launcher script for the Map Capture Application

This script provides a menu-driven interface to:
1. Test the installation
2. Run the main application
3. View help and documentation
"""

import sys
import os
import subprocess

def clear_screen():
    """Clear the terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """Print the application banner"""
    print("=" * 60)
    print("    MAP SCREENSHOT CAPTURE & STITCHING TOOL")
    print("=" * 60)
    print()

def print_menu():
    """Print the main menu"""
    print("Please select an option:")
    print()
    print("1. Test Installation")
    print("2. Run Web Automation Map Capture (Recommended)")
    print("3. Run Classic Map Capture (PyAutoGUI)")
    print("4. Install Dependencies")
    print("5. View Documentation")
    print("6. Exit")
    print()

def test_installation():
    """Run the installation test"""
    print("Running installation test...")
    print()
    try:
        subprocess.run([sys.executable, "test_installation.py"], check=True)
    except subprocess.CalledProcessError:
        print("Installation test failed. Please check the error messages above.")
    except FileNotFoundError:
        print("Error: test_installation.py not found in current directory.")
    
    input("\nPress Enter to continue...")

def run_web_application():
    """Run the web automation application"""
    print("Starting Web Automation Map Capture Application...")
    print("This version uses Selenium WebDriver for precise control.")
    print()
    try:
        subprocess.run([sys.executable, "web_map_capture_app.py"], check=True)
    except subprocess.CalledProcessError:
        print("Application failed to start. Please check the error messages above.")
    except FileNotFoundError:
        print("Error: web_map_capture_app.py not found in current directory.")
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")

    input("\nPress Enter to continue...")

def run_classic_application():
    """Run the classic PyAutoGUI application"""
    print("Starting Classic Map Capture Application...")
    print("This version uses PyAutoGUI for mouse control.")
    print()
    try:
        subprocess.run([sys.executable, "map_capture_app.py"], check=True)
    except subprocess.CalledProcessError:
        print("Application failed to start. Please check the error messages above.")
    except FileNotFoundError:
        print("Error: map_capture_app.py not found in current directory.")
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")

    input("\nPress Enter to continue...")

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    print("This will install both web automation and classic dependencies.")
    print()
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("\nDependencies installed successfully!")
        print("Installed: Selenium, WebDriver Manager, Pillow, Requests")
    except subprocess.CalledProcessError:
        print("Failed to install dependencies. Please check the error messages above.")
        print("You can try installing manually:")
        print("  pip install selenium webdriver-manager pillow requests")
    except FileNotFoundError:
        print("Error: requirements.txt not found in current directory.")

    input("\nPress Enter to continue...")

def view_documentation():
    """Display basic documentation"""
    clear_screen()
    print_banner()
    print("QUICK START GUIDE")
    print("-" * 20)
    print()
    print("1. PREPARATION:")
    print("   - Open your map application (Google Maps, etc.)")
    print("   - Navigate to the area you want to capture")
    print("   - Position the map at the TOP-LEFT corner of your desired area")
    print("   - Ensure the map fills your browser window")
    print()
    print("2. CONFIGURATION:")
    print("   - Set the number of rows and columns for your grid")
    print("   - Adjust the capture region if needed")
    print("   - Default region: 0, 100, 1920, 880 (for 1920x1080 screens)")
    print()
    print("3. CAPTURE PROCESS:")
    print("   - Click 'Start Capture' in the application")
    print("   - You have 5 seconds to switch to your map window")
    print("   - The application will automatically capture and stitch images")
    print()
    print("4. RESULTS:")
    print("   - Individual screenshots: screenshots/ folder")
    print("   - Final stitched image: stitched_map.png")
    print()
    print("TIPS:")
    print("- Start with a small grid (2x2 or 3x3) for testing")
    print("- Ensure good internet connection for fast map loading")
    print("- Use full-screen mode for best results")
    print("- Adjust capture region based on your screen resolution")
    print()
    print("For detailed documentation, see README.md")
    print()
    input("Press Enter to return to main menu...")

def main():
    """Main launcher function"""
    while True:
        clear_screen()
        print_banner()
        print_menu()
        
        try:
            choice = input("Enter your choice (1-5): ").strip()
            
            if choice == '1':
                clear_screen()
                print_banner()
                test_installation()
            elif choice == '2':
                clear_screen()
                print_banner()
                run_web_application()
            elif choice == '3':
                clear_screen()
                print_banner()
                run_classic_application()
            elif choice == '4':
                clear_screen()
                print_banner()
                install_dependencies()
            elif choice == '5':
                view_documentation()
            elif choice == '6':
                print("Thank you for using Map Capture Tool!")
                break
            else:
                print("Invalid choice. Please enter a number between 1 and 6.")
                input("Press Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"\nAn error occurred: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
