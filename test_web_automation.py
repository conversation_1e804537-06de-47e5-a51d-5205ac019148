#!/usr/bin/env python3
"""
Test script for web automation functionality

This script tests the web automation components to help debug
the "failed to pan right" issue.
"""

import sys
import time

def test_selenium_import():
    """Test if Selenium can be imported"""
    print("Testing Selenium import...")
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.common.action_chains import ActionChains
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ All Selenium imports successful")
        return True
    except ImportError as e:
        print(f"✗ Selenium import failed: {e}")
        return False

def test_chromedriver_setup():
    """Test ChromeDriver setup"""
    print("\nTesting ChromeDriver setup...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        # Install ChromeDriver
        driver_path = ChromeDriverManager().install()
        print(f"✓ ChromeDriver installed at: {driver_path}")
        
        # Test browser creation
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("✓ Chrome browser created successfully")
        
        # Test basic navigation
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✓ Navigation test successful - Page title: {title}")
        
        driver.quit()
        print("✓ Browser closed successfully")
        return True
        
    except Exception as e:
        print(f"✗ ChromeDriver test failed: {e}")
        return False

def test_hsac_map_access():
    """Test access to HSAC map"""
    print("\nTesting HSAC map access...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.common.by import By
        
        # Setup Chrome
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Navigate to HSAC map
        print("Navigating to HSAC map...")
        driver.get("https://hsac.org.in/eodb/")
        
        # Wait for page to load
        time.sleep(5)
        
        # Check page title
        title = driver.title
        print(f"✓ HSAC page loaded - Title: {title}")
        
        # Look for map elements
        map_selectors = [
            "canvas", "svg", ".map", "#map", 
            "[class*='map']", "[id*='map']"
        ]
        
        found_elements = []
        for selector in map_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                found_elements.append((selector, len(elements)))
        
        if found_elements:
            print("✓ Found map elements:")
            for selector, count in found_elements:
                print(f"  - {selector}: {count} elements")
        else:
            print("! No specific map elements found")
        
        # Test viewport size
        viewport_size = driver.execute_script(
            "return {width: window.innerWidth, height: window.innerHeight};"
        )
        print(f"✓ Viewport size: {viewport_size}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"✗ HSAC map test failed: {e}")
        return False

def test_action_chains():
    """Test ActionChains functionality"""
    print("\nTesting ActionChains...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.common.action_chains import ActionChains
        
        # Setup Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Navigate to a test page
        driver.get("https://www.google.com")
        
        # Test ActionChains
        actions = ActionChains(driver)
        actions.move_by_offset(100, 100)
        actions.click_and_hold()
        actions.move_by_offset(50, 0)
        actions.release()
        actions.perform()
        
        print("✓ ActionChains test successful")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"✗ ActionChains test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Web Automation Test Suite")
    print("=" * 40)
    
    tests = [
        ("Selenium Import", test_selenium_import),
        ("ChromeDriver Setup", test_chromedriver_setup),
        ("HSAC Map Access", test_hsac_map_access),
        ("ActionChains", test_action_chains)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n✓ All tests passed! Web automation should work.")
        print("\nIf you're still getting 'failed to pan right' errors:")
        print("1. Try using a smaller grid size (2x2)")
        print("2. Ensure the map is fully loaded before starting")
        print("3. Check that the map area is visible and interactive")
    else:
        print("\n✗ Some tests failed. Please check the errors above.")
        print("\nTo fix import issues:")
        print("1. Restart your IDE/editor")
        print("2. Check Python interpreter path")
        print("3. Reinstall dependencies: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
