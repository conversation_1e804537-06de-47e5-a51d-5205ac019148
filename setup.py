#!/usr/bin/env python3
"""
Setup script for Map Capture Application

This script helps users set up the application by:
1. Checking Python version
2. Installing dependencies
3. Testing the installation
4. Creating necessary directories
5. Providing setup guidance
"""

import sys
import os
import subprocess
import platform

def print_header():
    """Print setup header"""
    print("=" * 60)
    print("    MAP CAPTURE APPLICATION SETUP")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    version = sys.version_info
    
    if version.major >= 3 and version.minor >= 6:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.6+")
        print("Please install Python 3.6 or higher from https://python.org")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\nInstalling dependencies...")
    
    try:
        # Upgrade pip first
        print("Upgrading pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Install requirements
        print("Installing pyautogui and pillow...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyautogui", "pillow"], 
                      check=True, capture_output=True)
        
        print("✓ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        print("\nTry installing manually:")
        print("  pip install pyautogui pillow")
        return False

def test_imports():
    """Test if all required modules can be imported"""
    print("\nTesting module imports...")
    
    modules = ['tkinter', 'pyautogui', 'PIL', 'threading', 'time', 'os']
    all_good = True
    
    for module in modules:
        try:
            if module == 'PIL':
                import PIL
                from PIL import Image
            else:
                __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module} - {e}")
            all_good = False
    
    return all_good

def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    
    directories = ['screenshots']
    
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✓ Created directory: {directory}")
            else:
                print(f"✓ Directory already exists: {directory}")
        except Exception as e:
            print(f"✗ Failed to create directory {directory}: {e}")
            return False
    
    return True

def check_screen_resolution():
    """Check screen resolution and provide recommendations"""
    print("\nChecking screen resolution...")
    
    try:
        import pyautogui
        width, height = pyautogui.size()
        print(f"✓ Screen resolution: {width}x{height}")
        
        # Provide recommendations based on resolution
        common_regions = {
            (1920, 1080): (0, 100, 1920, 980),
            (1366, 768): (0, 100, 1366, 668),
            (2560, 1440): (0, 120, 2560, 1320),
            (3840, 2160): (0, 150, 3840, 2010),
            (1280, 720): (0, 90, 1280, 630)
        }
        
        if (width, height) in common_regions:
            region = common_regions[(width, height)]
            print(f"✓ Recommended capture region: {region}")
        else:
            # Calculate default region
            region = (0, 100, width, height - 100)
            print(f"✓ Suggested capture region: {region}")
            print("  (You may need to adjust this based on your browser)")
        
        return True
        
    except Exception as e:
        print(f"✗ Could not detect screen resolution: {e}")
        return False

def create_config_file():
    """Create a basic config file if it doesn't exist"""
    print("\nSetting up configuration...")
    
    if os.path.exists('config.py'):
        print("✓ config.py already exists")
        return True
    
    if os.path.exists('example_config.py'):
        try:
            # Copy example config to config.py
            with open('example_config.py', 'r') as src:
                content = src.read()
            
            with open('config.py', 'w') as dst:
                dst.write(content)
            
            print("✓ Created config.py from example_config.py")
            print("  You can customize config.py for your specific setup")
            return True
            
        except Exception as e:
            print(f"✗ Failed to create config.py: {e}")
            return False
    else:
        print("! example_config.py not found, using default settings")
        return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("SETUP COMPLETE!")
    print("=" * 60)
    print()
    print("Next steps:")
    print()
    print("1. QUICK START:")
    print("   python launcher.py")
    print("   (or double-click launcher.py)")
    print()
    print("2. DIRECT RUN:")
    print("   python map_capture_app.py")
    print("   (or double-click run_app.bat on Windows)")
    print()
    print("3. CONFIGURATION:")
    print("   - Edit config.py to customize settings")
    print("   - Adjust capture region for your screen/browser")
    print("   - Test with a small grid (2x2) first")
    print()
    print("4. USAGE TIPS:")
    print("   - Open your map in a browser")
    print("   - Position map at top-left of desired area")
    print("   - Use full-screen mode for best results")
    print("   - Ensure good internet connection")
    print()
    print("For detailed instructions, see README.md")
    print()

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return False
    
    # Install dependencies
    if not install_dependencies():
        input("Press Enter to exit...")
        return False
    
    # Test imports
    if not test_imports():
        print("\nSome modules failed to import. Please check the error messages above.")
        input("Press Enter to exit...")
        return False
    
    # Create directories
    if not create_directories():
        print("\nFailed to create necessary directories.")
        input("Press Enter to exit...")
        return False
    
    # Check screen resolution
    check_screen_resolution()
    
    # Create config file
    create_config_file()
    
    # Print next steps
    print_next_steps()
    
    input("Press Enter to exit...")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nSetup interrupted by user.")
    except Exception as e:
        print(f"\nAn error occurred during setup: {e}")
        input("Press Enter to exit...")
