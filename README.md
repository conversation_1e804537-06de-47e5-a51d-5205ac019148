# Map Screenshot Capture & Stitching Tool

A complete Python application that automates the process of taking multiple screenshots of a map and stitching them together into a single, large, high-resolution image. Now available in **two versions** for maximum precision and compatibility.

## 🚀 Two Versions Available

### 🌐 **Web Automation Version** (Recommended)
- **Selenium WebDriver**: Precise browser control for maximum accuracy
- **Direct Map Interaction**: No dependency on screen coordinates
- **Cross-Platform**: Works on any screen resolution and browser setup
- **Coordinate-Based**: Can work with specific lat/lng coordinates
- **Specialized for HSAC**: Optimized for https://hsac.org.in/eodb/ and similar map services

### 🖱️ **Classic Version** (PyAutoGUI)
- **Mouse Automation**: Traditional screen-based capture method
- **Universal Compatibility**: Works with any application or map service
- **Simple Setup**: No browser dependencies required
- **Lightweight**: Minimal resource usage

## Features (Both Versions)

- **Simple GUI**: Easy-to-use Tkinter interface
- **Automated Capture**: Systematic screenshot capture with precise control
- **Customizable Grid**: User-defined rows and columns for capture grid
- **Systematic Saving**: Screenshots saved with clear naming convention (`capture_row_column.png`)
- **Automatic Stitching**: Combines all screenshots into a single `stitched_map.png` file
- **Pre-capture Countdown**: 5-second countdown to prepare your setup
- **Progress Tracking**: Real-time status updates and progress bar
- **Error Handling**: Comprehensive error handling and user feedback

## Requirements

### Web Automation Version
- Python 3.6 or higher
- Selenium WebDriver (for browser automation)
- WebDriver Manager (automatic driver management)
- Pillow (PIL) (for image processing)
- Requests (for web communication)
- Tkinter (included with Python)

### Classic Version
- Python 3.6 or higher
- PyAutoGUI (for mouse control and screenshots)
- Pillow (PIL) (for image processing)
- Tkinter (included with Python)

## Installation

1. **Clone or download this repository**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
   
   Or install manually:
   ```bash
   pip install pyautogui pillow
   ```

## Usage

### Quick Start

1. **Run the application**:
   ```bash
   python map_capture_app.py
   ```

2. **Configure your capture**:
   - Set the number of rows and columns for your grid (default: 3x3)
   - Adjust the capture region if needed (default: 0, 100, 1920, 880)

3. **Prepare your map**:
   - Open your map application (Google Maps, etc.)
   - Position the map at the **top-left corner** of the area you want to capture
   - Make sure the map fills the capture region

4. **Start capture**:
   - Click "Start Capture"
   - You have 5 seconds to switch to your map window
   - The application will automatically capture screenshots and stitch them

### Detailed Instructions

#### Step 1: Configure Capture Settings

- **Number of Rows/Columns**: Determines how many screenshots will be taken in each direction
  - 3x3 grid = 9 total screenshots
  - 4x5 grid = 20 total screenshots
  - Maximum: 10x10 grid

- **Capture Region**: Defines the screen area to capture (x1, y1, x2, y2)
  - Default: `0, 100, 1920, 880` (full width, excluding top/bottom bars)
  - Adjust based on your screen resolution and browser layout
  - Example for 1366x768 screen: `0, 100, 1366, 668`

#### Step 2: Position Your Map

1. Open your map application in a web browser
2. Navigate to the area you want to capture
3. Position the map so the **top-left corner** of your desired area is visible
4. Ensure the map covers the entire capture region
5. **Important**: The map should be at an appropriate zoom level for your needs

#### Step 3: Capture Process

1. Click "Start Capture" in the application
2. Quickly switch to your map window (you have 5 seconds)
3. The application will:
   - Take a screenshot of the current view
   - Scroll horizontally to capture the next column
   - After completing a row, scroll vertically and reset horizontally
   - Continue until all grid positions are captured
   - Automatically stitch all images together

#### Step 4: Results

- Individual screenshots are saved in the `screenshots/` folder
- The final stitched image is saved as `stitched_map.png`
- Check the status messages for progress updates

## Tips for Best Results

### Map Preparation
- Use a consistent zoom level throughout the capture
- Ensure good internet connection for fast tile loading
- Close unnecessary browser tabs to improve performance
- Use full-screen mode or maximize your browser window

### Capture Region Setup
- Test with a small grid (2x2) first to verify your capture region
- Adjust the region to exclude browser toolbars and OS taskbars
- For Google Maps, typical regions:
  - 1920x1080 screen: `0, 100, 1920, 980`
  - 1366x768 screen: `0, 100, 1366, 668`

### Grid Size Considerations
- Larger grids provide higher resolution but take longer
- Consider your map's zoom level when choosing grid size
- Start with 3x3 or 4x4 for testing

## Troubleshooting

### Common Issues

**"PyAutoGUI fail-safe triggered"**
- Move your mouse to a corner of the screen to stop the process
- This is a safety feature to prevent runaway automation

**"Screenshots appear misaligned"**
- Adjust the scroll overlap by modifying the capture region
- Ensure your map loads completely between captures
- Try increasing the wait time between captures

**"Capture region is wrong"**
- Use a screenshot tool to determine exact coordinates
- Test with a small 2x2 grid first
- Adjust based on your browser and screen setup

**"Images don't stitch properly"**
- Ensure all screenshots are the same size
- Check that the capture region is consistent
- Verify that the map didn't change zoom levels during capture

### Performance Tips

- Close other applications to free up system resources
- Use a wired internet connection for faster map loading
- Increase wait times if your map loads slowly
- Consider capturing during off-peak hours for better map server response

## Technical Details

### File Structure
```
map_capture_app.py      # Main application
requirements.txt        # Python dependencies
README.md              # This file
screenshots/           # Individual screenshots (created automatically)
stitched_map.png       # Final stitched image (created after capture)
```

### Capture Logic
1. **Grid-based capture**: Systematic row-by-row, column-by-column approach
2. **Overlap handling**: 95% scroll distance to ensure overlap between images
3. **Position reset**: Horizontal position reset after each row
4. **Timing**: Built-in delays for map tile loading

### Image Stitching
- Uses PIL (Pillow) for image manipulation
- Creates a large canvas based on grid dimensions
- Pastes images at calculated positions
- Saves final image with high quality (95% JPEG quality)

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.
