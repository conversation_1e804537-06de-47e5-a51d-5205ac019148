#!/usr/bin/env python3
"""
Debug script to test panning functionality specifically

This script will help identify why the "failed to pan right" error occurs.
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

def setup_browser():
    """Setup Chrome browser"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Remove automation indicators
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    except Exception as e:
        print(f"Browser setup failed: {e}")
        return None

def load_hsac_map(driver):
    """Load HSAC map and analyze elements"""
    try:
        print("Loading HSAC map...")
        driver.get("https://hsac.org.in/eodb/")
        
        # Wait for page to load
        time.sleep(8)  # Longer wait for HSAC
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Analyze map elements
        analyze_map_elements(driver)
        
        return True
    except Exception as e:
        print(f"Failed to load HSAC map: {e}")
        return False

def analyze_map_elements(driver):
    """Analyze available map elements"""
    print("\nAnalyzing map elements...")
    
    selectors = [
        "#map",
        "canvas", 
        "svg",
        ".map-container",
        ".leaflet-container",
        ".ol-viewport",
        "[class*='map']",
        "[id*='map']"
    ]
    
    for selector in selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"\n{selector}: {len(elements)} elements found")
                for i, element in enumerate(elements[:3]):  # Show first 3
                    try:
                        size = element.size
                        location = element.location
                        is_displayed = element.is_displayed()
                        is_enabled = element.is_enabled()
                        tag_name = element.tag_name
                        
                        print(f"  Element {i}: {tag_name}")
                        print(f"    Size: {size['width']}x{size['height']}")
                        print(f"    Location: ({location['x']}, {location['y']})")
                        print(f"    Displayed: {is_displayed}, Enabled: {is_enabled}")
                        
                        # Get some attributes
                        class_attr = element.get_attribute('class')
                        id_attr = element.get_attribute('id')
                        if class_attr:
                            print(f"    Class: {class_attr[:50]}...")
                        if id_attr:
                            print(f"    ID: {id_attr}")
                            
                    except Exception as e:
                        print(f"    Error getting element info: {e}")
        except Exception as e:
            print(f"Error with selector {selector}: {e}")

def find_best_map_element(driver):
    """Find the best element for panning"""
    print("\nFinding best map element for panning...")
    
    # Priority selectors for HSAC
    priority_selectors = ["#map", "canvas"]
    
    for selector in priority_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for element in elements:
                size = element.size
                if size['width'] > 200 and size['height'] > 200 and element.is_displayed():
                    print(f"Selected element: {selector}")
                    print(f"  Size: {size['width']}x{size['height']}")
                    print(f"  Location: {element.location}")
                    return element
        except Exception as e:
            print(f"Error checking {selector}: {e}")
    
    print("No suitable map element found")
    return None

def test_pan_operations(driver, map_element):
    """Test different panning methods"""
    print("\nTesting pan operations...")
    
    if not map_element:
        print("No map element available for testing")
        return False
    
    # Test parameters
    pan_distance = 100
    
    # Method 1: Direct element drag
    print("\n1. Testing direct element drag...")
    try:
        actions = ActionChains(driver)
        actions.move_to_element(map_element)
        actions.click_and_hold()
        actions.move_by_offset(-pan_distance, 0)  # Pan right
        actions.release()
        actions.perform()
        
        time.sleep(2)
        print("✓ Direct element drag successful")
        
        # Pan back
        actions = ActionChains(driver)
        actions.move_to_element(map_element)
        actions.click_and_hold()
        actions.move_by_offset(pan_distance, 0)  # Pan left (back)
        actions.release()
        actions.perform()
        time.sleep(2)
        
    except Exception as e:
        print(f"✗ Direct element drag failed: {e}")
    
    # Method 2: Click then drag
    print("\n2. Testing click then drag...")
    try:
        actions = ActionChains(driver)
        actions.move_to_element(map_element)
        actions.click()
        actions.pause(0.5)
        actions.click_and_hold()
        actions.move_by_offset(-pan_distance, 0)  # Pan right
        actions.release()
        actions.perform()
        
        time.sleep(2)
        print("✓ Click then drag successful")
        
        # Pan back
        actions = ActionChains(driver)
        actions.move_to_element(map_element)
        actions.click_and_hold()
        actions.move_by_offset(pan_distance, 0)  # Pan left (back)
        actions.release()
        actions.perform()
        time.sleep(2)
        
    except Exception as e:
        print(f"✗ Click then drag failed: {e}")
    
    # Method 3: JavaScript scroll
    print("\n3. Testing JavaScript scroll...")
    try:
        driver.execute_script("window.scrollBy(100, 0);")
        time.sleep(2)
        print("✓ JavaScript scroll successful")
        
        # Scroll back
        driver.execute_script("window.scrollBy(-100, 0);")
        time.sleep(2)
        
    except Exception as e:
        print(f"✗ JavaScript scroll failed: {e}")
    
    return True

def main():
    """Main test function"""
    print("HSAC Map Panning Debug Test")
    print("=" * 40)
    
    # Setup browser
    driver = setup_browser()
    if not driver:
        print("Failed to setup browser")
        return
    
    try:
        # Load HSAC map
        if not load_hsac_map(driver):
            print("Failed to load HSAC map")
            return
        
        # Find map element
        map_element = find_best_map_element(driver)
        
        # Test panning
        test_pan_operations(driver, map_element)
        
        print("\n" + "=" * 40)
        print("Debug test completed!")
        print("\nIf any method worked, that's the one to use in the main application.")
        print("If none worked, the issue might be:")
        print("1. Map not fully loaded")
        print("2. Map requires specific interaction sequence")
        print("3. Map has pan restrictions")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
