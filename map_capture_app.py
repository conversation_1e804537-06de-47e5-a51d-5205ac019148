#!/usr/bin/env python3
"""
Map Screenshot Capture and Stitching Application

A complete Python application that automates the process of taking multiple 
screenshots of a map and stitching them together into a single high-resolution image.

Features:
- Simple GUI with Tkinter
- Automated screenshot capture with PyAutoGUI
- Systematic image saving and stitching with Pillow
- User-defined grid dimensions
- Pre-capture countdown
- Status updates throughout the process
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
from PIL import Image
import time
import threading
import os
import sys
try:
    from config import *
except ImportError:
    # Fallback values if config.py is not available
    DEFAULT_ROWS = 3
    DEFAULT_COLS = 3
    COUNTDOWN_TIME = 5
    SCROLL_WAIT_TIME = 2
    HORIZONTAL_RESET_WAIT = 1
    SCROLL_OVERLAP_FACTOR = 0.95
    SCROLL_DURATION = 0.5
    SCREENSHOTS_DIR = "screenshots"
    FINAL_IMAGE_NAME = "stitched_map.png"
    WINDOW_TITLE = "Map Screenshot Capture & Stitching Tool"
    WINDOW_SIZE = "400x300"
    KEEP_ON_TOP = True
    PYAUTOGUI_PAUSE = 0.5
    PYAUTOGUI_FAILSAFE = True


class MapCaptureApp:
    def __init__(self, root):
        self.root = root
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)
        self.root.resizable(False, False)

        # Keep window on top
        self.root.attributes('-topmost', KEEP_ON_TOP)

        # Initialize variables
        self.is_capturing = False
        self.capture_region = (0, 100, 1920, 880)  # Default capture region

        # Configure PyAutoGUI
        pyautogui.FAILSAFE = PYAUTOGUI_FAILSAFE
        pyautogui.PAUSE = PYAUTOGUI_PAUSE

        self.setup_gui()
    
    def setup_gui(self):
        """Create and configure the GUI elements"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Map Screenshot Capture Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Grid configuration
        ttk.Label(main_frame, text="Grid Configuration:", 
                 font=("Arial", 12, "bold")).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # Rows input
        ttk.Label(main_frame, text="Number of Rows:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.rows_var = tk.StringVar(value=str(DEFAULT_ROWS))
        self.rows_entry = ttk.Entry(main_frame, textvariable=self.rows_var, width=10)
        self.rows_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Columns input
        ttk.Label(main_frame, text="Number of Columns:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.cols_var = tk.StringVar(value=str(DEFAULT_COLS))
        self.cols_entry = ttk.Entry(main_frame, textvariable=self.cols_var, width=10)
        self.cols_entry.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Capture region configuration
        ttk.Label(main_frame, text="Capture Region (x1, y1, x2, y2):", 
                 font=("Arial", 10)).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(20, 5))
        self.region_var = tk.StringVar(value="0, 100, 1920, 880")
        self.region_entry = ttk.Entry(main_frame, textvariable=self.region_var, width=30)
        self.region_entry.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Start button
        self.start_button = ttk.Button(main_frame, text="Start Capture", 
                                      command=self.start_capture_process)
        self.start_button.grid(row=6, column=0, columnspan=2, pady=20)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready - Position your map at the top-left corner")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                     font=("Arial", 10), foreground="blue")
        self.status_label.grid(row=7, column=0, columnspan=2, pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='determinate')
        self.progress.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def validate_inputs(self):
        """Validate user inputs"""
        try:
            rows = int(self.rows_var.get())
            cols = int(self.cols_var.get())
            
            if rows < 1 or cols < 1:
                raise ValueError("Rows and columns must be positive integers")
            
            if rows > MAX_GRID_SIZE or cols > MAX_GRID_SIZE:
                raise ValueError(f"Maximum {MAX_GRID_SIZE} rows and {MAX_GRID_SIZE} columns allowed")
            
            # Parse capture region
            region_str = self.region_var.get().replace(" ", "")
            region_parts = region_str.split(",")
            if len(region_parts) != 4:
                raise ValueError("Capture region must have 4 values: x1, y1, x2, y2")
            
            region = tuple(int(x) for x in region_parts)
            if region[2] <= region[0] or region[3] <= region[1]:
                raise ValueError("Invalid capture region coordinates")
            
            return rows, cols, region
            
        except ValueError as e:
            messagebox.showerror("Input Error", str(e))
            return None, None, None
    
    def start_capture_process(self):
        """Start the capture process with validation and countdown"""
        if self.is_capturing:
            return
        
        rows, cols, region = self.validate_inputs()
        if rows is None:
            return
        
        self.is_capturing = True
        self.start_button.config(state='disabled')
        self.capture_region = region
        
        # Start countdown in a separate thread
        threading.Thread(target=self.countdown_and_capture, 
                        args=(rows, cols), daemon=True).start()
    
    def countdown_and_capture(self, rows, cols):
        """Countdown and then start the capture process"""
        try:
            # Countdown
            for i in range(COUNTDOWN_TIME, 0, -1):
                self.status_var.set(f"Starting in {i}... Position your map now!")
                time.sleep(1)
            
            self.status_var.set("Starting capture process...")
            self.capture_screenshots(rows, cols)
            
        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            messagebox.showerror("Capture Error", f"An error occurred: {str(e)}")
        finally:
            self.is_capturing = False
            self.start_button.config(state='normal')
            self.progress['value'] = 0
    
    def capture_screenshots(self, rows, cols):
        """Main capture logic"""
        total_images = rows * cols
        current_image = 0
        
        # Calculate capture dimensions and scroll distances
        capture_width = self.capture_region[2] - self.capture_region[0]
        capture_height = self.capture_region[3] - self.capture_region[1]
        scroll_x = int(capture_width * SCROLL_OVERLAP_FACTOR)
        scroll_y = int(capture_height * SCROLL_OVERLAP_FACTOR)
        
        # Center points for scrolling
        center_x = self.capture_region[0] + capture_width // 2
        center_y = self.capture_region[1] + capture_height // 2
        
        self.progress['maximum'] = total_images
        
        # Create screenshots directory if it doesn't exist
        if not os.path.exists(SCREENSHOTS_DIR):
            os.makedirs(SCREENSHOTS_DIR)
        
        # Main capture loops
        for row in range(rows):
            for col in range(cols):
                current_image += 1
                self.status_var.set(f"Capturing image {current_image} of {total_images} (Row {row+1}, Col {col+1})")
                self.progress['value'] = current_image
                self.root.update()
                
                # Take screenshot
                screenshot = pyautogui.screenshot(region=self.capture_region)
                filename = f"{SCREENSHOTS_DIR}/capture_{row}_{col}.png"
                screenshot.save(filename)

                # Horizontal scroll (if not last column)
                if col < cols - 1:
                    self.scroll_horizontal(center_x, center_y, scroll_x)
                    time.sleep(SCROLL_WAIT_TIME)  # Wait for map tiles to load
            
            # Vertical scroll and horizontal reset (if not last row)
            if row < rows - 1:
                self.scroll_vertical(center_x, center_y, scroll_y)
                time.sleep(SCROLL_WAIT_TIME)

                # Reset horizontal position (scroll back to left)
                for _ in range(cols - 1):
                    self.scroll_horizontal(center_x, center_y, -scroll_x)
                    time.sleep(HORIZONTAL_RESET_WAIT)
        
        # Start stitching process
        self.status_var.set("Stitching images together...")
        self.root.update()
        self.stitch_images(rows, cols)
        
        self.status_var.set(f"Process completed! Check {FINAL_IMAGE_NAME}")
        messagebox.showinfo("Success", f"Map capture and stitching completed successfully!\nCheck '{FINAL_IMAGE_NAME}' in the current directory.")
    
    def scroll_horizontal(self, center_x, center_y, distance):
        """Perform horizontal scroll by dragging"""
        pyautogui.drag(-distance, 0, duration=SCROLL_DURATION, button='left')

    def scroll_vertical(self, center_x, center_y, distance):
        """Perform vertical scroll by dragging"""
        pyautogui.drag(0, -distance, duration=SCROLL_DURATION, button='left')
    
    def stitch_images(self, rows, cols):
        """Stitch all captured images into one large image"""
        try:
            # Get dimensions from first image
            first_image = Image.open(f"{SCREENSHOTS_DIR}/capture_0_0.png")
            img_width, img_height = first_image.size
            first_image.close()

            # Create large canvas
            total_width = img_width * cols
            total_height = img_height * rows
            stitched_image = Image.new('RGB', (total_width, total_height))

            # Paste all images
            for row in range(rows):
                for col in range(cols):
                    filename = f"{SCREENSHOTS_DIR}/capture_{row}_{col}.png"
                    if os.path.exists(filename):
                        img = Image.open(filename)
                        paste_x = col * img_width
                        paste_y = row * img_height
                        stitched_image.paste(img, (paste_x, paste_y))
                        img.close()

            # Save final stitched image
            stitched_image.save(FINAL_IMAGE_NAME, quality=95)
            stitched_image.close()
            
        except Exception as e:
            raise Exception(f"Stitching failed: {str(e)}")


def main():
    """Main function to run the application"""
    # Check if required modules are available
    try:
        import pyautogui
        import PIL
    except ImportError as e:
        print(f"Missing required module: {e}")
        print("Please install required packages:")
        print("pip install pyautogui pillow")
        sys.exit(1)
    
    root = tk.Tk()
    app = MapCaptureApp(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)


if __name__ == "__main__":
    main()
