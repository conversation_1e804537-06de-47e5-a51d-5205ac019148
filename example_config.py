"""
Example Configuration File for Map Capture Application

Copy this file to 'config.py' and modify the values to match your setup.
The application will use these settings instead of the defaults.

IMPORTANT: Rename this file to 'config.py' for the settings to take effect.
"""

# =============================================================================
# GRID SETTINGS
# =============================================================================

# Default number of rows and columns for the capture grid
DEFAULT_ROWS = 3
DEFAULT_COLS = 3

# Maximum allowed grid size (prevents accidentally creating huge grids)
MAX_GRID_SIZE = 10

# =============================================================================
# TIMING SETTINGS (in seconds)
# =============================================================================

# Countdown time before starting capture (gives you time to switch windows)
COUNTDOWN_TIME = 5

# Wait time after scrolling for map tiles to load
SCROLL_WAIT_TIME = 2

# Wait time during horizontal position reset
HORIZONTAL_RESET_WAIT = 1

# =============================================================================
# SCROLL SETTINGS
# =============================================================================

# How much to scroll relative to capture area (0.95 = 95% overlap)
# Lower values = more overlap, higher values = less overlap
SCROLL_OVERLAP_FACTOR = 0.95

# Duration of scroll animation in seconds
SCROLL_DURATION = 0.5

# =============================================================================
# CAPTURE REGIONS FOR DIFFERENT SCREEN RESOLUTIONS
# =============================================================================

# Format: (x1, y1, x2, y2) where:
# x1, y1 = top-left corner coordinates
# x2, y2 = bottom-right corner coordinates

# Common screen resolutions with recommended capture regions
CAPTURE_REGIONS = {
    # Full HD (1920x1080) - most common
    "1920x1080": (0, 100, 1920, 980),
    
    # Common laptop resolution
    "1366x768": (0, 100, 1366, 668),
    
    # 1440p resolution
    "2560x1440": (0, 120, 2560, 1320),
    
    # 4K resolution
    "3840x2160": (0, 150, 3840, 2010),
    
    # HD resolution
    "1280x720": (0, 90, 1280, 630),
    
    # Custom region - modify this for your specific setup
    "custom": (0, 100, 1920, 880)
}

# =============================================================================
# BROWSER-SPECIFIC CAPTURE REGIONS
# =============================================================================

# Adjust these based on your browser and its toolbar configuration
BROWSER_REGIONS = {
    # Google Chrome
    "chrome_fullscreen": (0, 0, 1920, 1080),
    "chrome_windowed": (0, 100, 1920, 980),
    
    # Mozilla Firefox
    "firefox_fullscreen": (0, 0, 1920, 1080),
    "firefox_windowed": (0, 110, 1920, 970),
    
    # Microsoft Edge
    "edge_windowed": (0, 105, 1920, 975),
    
    # Safari (macOS)
    "safari_windowed": (0, 95, 1920, 985)
}

# =============================================================================
# FILE AND DIRECTORY SETTINGS
# =============================================================================

# Directory where individual screenshots are saved
SCREENSHOTS_DIR = "screenshots"

# Name of the final stitched image
FINAL_IMAGE_NAME = "stitched_map.png"

# Format for individual screenshots (PNG recommended for quality)
SCREENSHOT_FORMAT = "PNG"

# Format for final stitched image
FINAL_IMAGE_FORMAT = "PNG"

# Quality for JPEG images (only used if format is JPEG)
FINAL_IMAGE_QUALITY = 95

# =============================================================================
# PYAUTOGUI SETTINGS
# =============================================================================

# Pause between PyAutoGUI actions (prevents actions from happening too fast)
PYAUTOGUI_PAUSE = 0.5

# Enable fail-safe (move mouse to corner to stop the process)
PYAUTOGUI_FAILSAFE = True

# =============================================================================
# GUI SETTINGS
# =============================================================================

# Application window title
WINDOW_TITLE = "Map Screenshot Capture & Stitching Tool"

# Application window size (width x height)
WINDOW_SIZE = "400x300"

# Keep application window on top of other windows
KEEP_ON_TOP = True

# =============================================================================
# MAP SERVICE SPECIFIC SETTINGS
# =============================================================================

# Settings optimized for different map services
MAP_SERVICES = {
    "google_maps": {
        "recommended_region": (0, 100, 1920, 980),
        "scroll_wait_time": 2,
        "notes": "Works best in full-screen mode"
    },
    "openstreetmap": {
        "recommended_region": (0, 80, 1920, 1000),
        "scroll_wait_time": 1.5,
        "notes": "Usually loads faster than Google Maps"
    },
    "bing_maps": {
        "recommended_region": (0, 120, 1920, 960),
        "scroll_wait_time": 2.5,
        "notes": "May need longer wait times"
    }
}

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_capture_region_for_resolution(width, height):
    """
    Get the recommended capture region for a given screen resolution.
    
    Args:
        width (int): Screen width in pixels
        height (int): Screen height in pixels
    
    Returns:
        tuple: (x1, y1, x2, y2) capture region coordinates
    """
    resolution_key = f"{width}x{height}"
    
    if resolution_key in CAPTURE_REGIONS:
        return CAPTURE_REGIONS[resolution_key]
    
    # Calculate a reasonable default for unknown resolutions
    # Exclude top 100px for browser toolbar and bottom 100px for taskbar
    return (0, 100, width, height - 100)

def get_recommended_grid_size(capture_width, capture_height, target_resolution=2000):
    """
    Calculate recommended grid size based on capture area and target resolution.
    
    Args:
        capture_width (int): Width of capture region
        capture_height (int): Height of capture region
        target_resolution (int): Target pixel count for final image width/height
    
    Returns:
        tuple: (recommended_rows, recommended_cols)
    """
    # Calculate how many captures needed to reach target resolution
    cols = max(1, min(MAX_GRID_SIZE, target_resolution // capture_width))
    rows = max(1, min(MAX_GRID_SIZE, target_resolution // capture_height))
    
    return rows, cols

# =============================================================================
# CUSTOMIZATION NOTES
# =============================================================================

"""
HOW TO CUSTOMIZE FOR YOUR SETUP:

1. SCREEN RESOLUTION:
   - Find your screen resolution in CAPTURE_REGIONS
   - If not found, add a new entry or modify "custom"
   - Format: "WIDTHxHEIGHT": (x1, y1, x2, y2)

2. BROWSER SETUP:
   - Use browser-specific regions from BROWSER_REGIONS
   - Adjust based on your browser's toolbar height
   - Test with a small grid first (2x2)

3. TIMING ADJUSTMENTS:
   - Increase SCROLL_WAIT_TIME if your internet is slow
   - Increase COUNTDOWN_TIME if you need more setup time
   - Adjust SCROLL_OVERLAP_FACTOR for more/less image overlap

4. TESTING YOUR SETUP:
   - Start with DEFAULT_ROWS = 2, DEFAULT_COLS = 2
   - Use a small area of the map for testing
   - Check if images align properly in the final result
   - Adjust settings based on results

5. PERFORMANCE OPTIMIZATION:
   - Close unnecessary browser tabs
   - Use wired internet connection
   - Capture during off-peak hours for better map server response
"""
