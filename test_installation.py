#!/usr/bin/env python3
"""
Test script to verify that all required dependencies are installed
and the system is ready to run the Map Capture application.
"""

import sys
import os

def test_python_version():
    """Test if Python version is compatible"""
    print("Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 6:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.6+")
        return False

def test_imports():
    """Test if all required modules can be imported"""
    print("\nTesting required modules...")
    
    modules = {
        'tkinter': 'GUI framework (should be included with Python)',
        'selenium': 'Web automation framework',
        'webdriver_manager': 'Automatic WebDriver management',
        'PIL': 'Image processing (Pillow)',
        'requests': 'HTTP library for web communication',
        'threading': 'Multi-threading support (built-in)',
        'time': 'Time utilities (built-in)',
        'os': 'Operating system interface (built-in)'
    }
    
    all_good = True
    
    for module, description in modules.items():
        try:
            if module == 'PIL':
                import PIL
                from PIL import Image
            else:
                __import__(module)
            print(f"✓ {module} - {description}")
        except ImportError as e:
            print(f"✗ {module} - {description} - ERROR: {e}")
            all_good = False
    
    return all_good

def test_web_automation_features():
    """Test web automation features"""
    print("\nTesting Web Automation features...")

    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager

        # Test ChromeDriver installation
        try:
            driver_path = ChromeDriverManager().install()
            print(f"✓ ChromeDriver installed: {driver_path}")
        except Exception as e:
            print(f"✗ ChromeDriver installation failed: {e}")
            return False

        # Test Chrome options
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background for testing
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            print("✓ Chrome options configured")
        except Exception as e:
            print(f"✗ Chrome options failed: {e}")
            return False

        print("✓ Web automation ready (Chrome browser required for full functionality)")
        return True

    except ImportError as e:
        print(f"✗ Web automation not available: {e}")
        return False

def test_file_permissions():
    """Test if we can create files in the current directory"""
    print("\nTesting file permissions...")
    
    try:
        # Test creating a directory
        test_dir = "test_screenshots"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        print(f"✓ Can create directories")
        
        # Test creating a file
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        print(f"✓ Can create files")
        
        # Clean up
        os.remove(test_file)
        os.rmdir(test_dir)
        print(f"✓ Can delete files and directories")
        
        return True
        
    except Exception as e:
        print(f"✗ File permission test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Map Capture Application - Installation Test")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_imports,
        test_web_automation_features,
        test_file_permissions
    ]
    
    all_passed = True
    
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("✓ All tests passed! Your system is ready to run the Map Capture applications.")
        print("\nTo start the applications, run:")
        print("  python web_map_capture_app.py  (Web Automation - Recommended)")
        print("  python map_capture_app.py      (Classic PyAutoGUI)")
        print("  python launcher.py             (Menu-driven launcher)")
    else:
        print("✗ Some tests failed. Please install missing dependencies:")
        print("\n  pip install -r requirements.txt")
        print("\nOr install manually:")
        print("  pip install selenium webdriver-manager pillow requests")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
