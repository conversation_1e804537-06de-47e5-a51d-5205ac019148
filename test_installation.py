#!/usr/bin/env python3
"""
Test script to verify that all required dependencies are installed
and the system is ready to run the Map Capture application.
"""

import sys
import os

def test_python_version():
    """Test if Python version is compatible"""
    print("Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 6:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.6+")
        return False

def test_imports():
    """Test if all required modules can be imported"""
    print("\nTesting required modules...")
    
    modules = {
        'tkinter': 'GUI framework (should be included with Python)',
        'pyautogui': 'Mouse automation and screenshots',
        'PIL': 'Image processing (Pillow)',
        'threading': 'Multi-threading support (built-in)',
        'time': 'Time utilities (built-in)',
        'os': 'Operating system interface (built-in)'
    }
    
    all_good = True
    
    for module, description in modules.items():
        try:
            if module == 'PIL':
                import PIL
                from PIL import Image
            else:
                __import__(module)
            print(f"✓ {module} - {description}")
        except ImportError as e:
            print(f"✗ {module} - {description} - ERROR: {e}")
            all_good = False
    
    return all_good

def test_pyautogui_features():
    """Test specific PyAutoGUI features"""
    print("\nTesting PyAutoGUI features...")
    
    try:
        import pyautogui
        
        # Test screenshot capability
        try:
            # Take a small test screenshot
            screenshot = pyautogui.screenshot(region=(0, 0, 100, 100))
            print("✓ Screenshot functionality working")
            
            # Test if we can get screen size
            width, height = pyautogui.size()
            print(f"✓ Screen size detection: {width}x{height}")
            
        except Exception as e:
            print(f"✗ Screenshot test failed: {e}")
            return False
            
        # Test mouse position
        try:
            x, y = pyautogui.position()
            print(f"✓ Mouse position detection: ({x}, {y})")
        except Exception as e:
            print(f"✗ Mouse position test failed: {e}")
            return False
            
        return True
        
    except ImportError:
        print("✗ PyAutoGUI not available")
        return False

def test_file_permissions():
    """Test if we can create files in the current directory"""
    print("\nTesting file permissions...")
    
    try:
        # Test creating a directory
        test_dir = "test_screenshots"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        print(f"✓ Can create directories")
        
        # Test creating a file
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        print(f"✓ Can create files")
        
        # Clean up
        os.remove(test_file)
        os.rmdir(test_dir)
        print(f"✓ Can delete files and directories")
        
        return True
        
    except Exception as e:
        print(f"✗ File permission test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Map Capture Application - Installation Test")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_imports,
        test_pyautogui_features,
        test_file_permissions
    ]
    
    all_passed = True
    
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("✓ All tests passed! Your system is ready to run the Map Capture application.")
        print("\nTo start the application, run:")
        print("  python map_capture_app.py")
        print("  or double-click run_app.bat (Windows)")
    else:
        print("✗ Some tests failed. Please install missing dependencies:")
        print("\n  pip install -r requirements.txt")
        print("\nOr install manually:")
        print("  pip install pyautogui pillow")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
