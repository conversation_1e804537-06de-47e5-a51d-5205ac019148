#!/usr/bin/env python3
"""
Web Automation Map Screenshot Capture and Stitching Application

A precision-focused Python application that uses Selenium WebDriver to automate
map screenshot capture and stitching for maximum accuracy and reliability.

Features:
- Selenium WebDriver for precise browser control
- Direct map element interaction
- Coordinate-based navigation
- JavaScript injection for exact positioning
- Enhanced GUI with coordinate input
- Specialized HSAC map integration
- High-precision screenshot capture
"""

import tkinter as tk
from tkinter import ttk, messagebox
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image
import time
import threading
import os
import sys
import io
import base64
import requests
import json

try:
    from config import *
except ImportError:
    # Fallback values if config.py is not available
    DEFAULT_ROWS = 3
    DEFAULT_COLS = 3
    COUNTDOWN_TIME = 5
    SCROLL_WAIT_TIME = 3
    SCREENSHOTS_DIR = "screenshots"
    FINAL_IMAGE_NAME = "stitched_map.png"
    WINDOW_TITLE = "Web Map Capture & Stitching Tool"
    WINDOW_SIZE = "500x600"
    KEEP_ON_TOP = True


class WebMapController:
    """Handles all web automation and map interaction"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.map_url = "https://hsac.org.in/eodb/"
        self.map_loaded = False
        
    def setup_browser(self):
        """Initialize Chrome browser with optimal settings"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            
            # Install and setup ChromeDriver automatically
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Remove automation indicators
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 20)
            return True
            
        except Exception as e:
            print(f"Browser setup failed: {e}")
            return False
    
    def load_map(self):
        """Load the HSAC map and wait for it to be ready"""
        try:
            self.driver.get(self.map_url)
            
            # Wait for the page to load completely
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Wait a bit more for map tiles to load
            time.sleep(5)
            
            # Try to find map container or canvas
            self.wait_for_map_ready()
            
            self.map_loaded = True
            return True
            
        except Exception as e:
            print(f"Map loading failed: {e}")
            return False
    
    def wait_for_map_ready(self):
        """Wait for map to be fully loaded and interactive"""
        try:
            # Wait for common map elements to be present
            print("Waiting for map to load...")
            time.sleep(5)  # Increased wait time for HSAC

            # Check if we can find any map-related elements
            map_selectors = [
                "canvas", "svg", ".map", "#map",
                ".leaflet-container", ".ol-viewport",
                "[class*='map']", "[id*='map']"
            ]

            found_elements = []
            for selector in map_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    found_elements.extend([(selector, len(elements))])

            if found_elements:
                print("Found map elements:")
                for selector, count in found_elements:
                    print(f"  - {selector}: {count} elements")
                return True
            else:
                print("No specific map elements found, checking page readiness...")
                # Check if page is at least loaded
                ready_state = self.driver.execute_script("return document.readyState")
                print(f"Page ready state: {ready_state}")
                return ready_state == "complete"

        except Exception as e:
            print(f"Map ready check failed: {e}")
            return False
    
    def get_map_bounds(self):
        """Get the current map viewport bounds"""
        try:
            # Get viewport size
            viewport_size = self.driver.execute_script(
                "return {width: window.innerWidth, height: window.innerHeight};"
            )
            
            # Calculate map area (excluding UI elements)
            # Assuming map takes most of the viewport
            map_bounds = {
                'x': 50,  # Leave some margin for UI
                'y': 100,
                'width': viewport_size['width'] - 100,
                'height': viewport_size['height'] - 150
            }
            
            return map_bounds
            
        except Exception as e:
            print(f"Failed to get map bounds: {e}")
            return {'x': 50, 'y': 100, 'width': 1200, 'height': 800}
    
    def pan_map(self, direction, distance_pixels):
        """Pan the map in specified direction by given distance"""
        try:
            # Method 1: Try to find specific map elements first
            map_element = self.find_map_element()

            if map_element:
                return self.pan_map_element(map_element, direction, distance_pixels)
            else:
                # Method 2: Fallback to viewport-based panning
                return self.pan_map_viewport(direction, distance_pixels)

        except Exception as e:
            print(f"Pan operation failed: {e}")
            # Method 3: Last resort - JavaScript-based panning
            return self.pan_map_javascript(direction, distance_pixels)

    def find_map_element(self):
        """Find the main map element for interaction"""
        try:
            # Try different selectors for map elements
            selectors = [
                "canvas",  # Most web maps use canvas
                "svg",     # Some use SVG
                ".map-container",
                "#map",
                ".leaflet-container",
                ".ol-viewport",
                ".mapboxgl-canvas",
                "[class*='map']",
                "[id*='map']"
            ]

            for selector in selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Return the largest element (likely the main map)
                    largest_element = max(elements, key=lambda e: e.size['width'] * e.size['height'])
                    print(f"Found map element using selector: {selector}")
                    return largest_element

            return None

        except Exception as e:
            print(f"Map element detection failed: {e}")
            return None

    def pan_map_element(self, map_element, direction, distance_pixels):
        """Pan using direct map element interaction"""
        try:
            # Calculate pan offset based on direction
            if direction == 'right':
                offset_x, offset_y = -distance_pixels, 0  # Negative to pan right
            elif direction == 'left':
                offset_x, offset_y = distance_pixels, 0   # Positive to pan left
            elif direction == 'down':
                offset_x, offset_y = 0, -distance_pixels  # Negative to pan down
            elif direction == 'up':
                offset_x, offset_y = 0, distance_pixels   # Positive to pan up
            else:
                return False

            # Get element center
            element_rect = map_element.rect
            center_x = element_rect['width'] // 2
            center_y = element_rect['height'] // 2

            # Perform drag operation on the map element
            actions = ActionChains(self.driver)
            actions.move_to_element_with_offset(map_element, center_x, center_y)
            actions.click_and_hold()
            actions.move_by_offset(offset_x, offset_y)
            actions.release()
            actions.perform()

            # Wait for map to settle
            time.sleep(SCROLL_WAIT_TIME)
            print(f"Successfully panned {direction} using map element")
            return True

        except Exception as e:
            print(f"Element-based pan failed: {e}")
            return False

    def pan_map_viewport(self, direction, distance_pixels):
        """Pan using viewport coordinates as fallback"""
        try:
            map_bounds = self.get_map_bounds()
            center_x = map_bounds['x'] + map_bounds['width'] // 2
            center_y = map_bounds['y'] + map_bounds['height'] // 2

            # Calculate pan offset based on direction
            if direction == 'right':
                offset_x, offset_y = -distance_pixels, 0
            elif direction == 'left':
                offset_x, offset_y = distance_pixels, 0
            elif direction == 'down':
                offset_x, offset_y = 0, -distance_pixels
            elif direction == 'up':
                offset_x, offset_y = 0, distance_pixels
            else:
                return False

            # Perform drag operation using absolute coordinates
            actions = ActionChains(self.driver)
            actions.move_by_offset(center_x, center_y)
            actions.click_and_hold()
            actions.move_by_offset(offset_x, offset_y)
            actions.release()
            actions.perform()

            # Wait for map to settle
            time.sleep(SCROLL_WAIT_TIME)
            print(f"Successfully panned {direction} using viewport method")
            return True

        except Exception as e:
            print(f"Viewport-based pan failed: {e}")
            return False

    def pan_map_javascript(self, direction, distance_pixels):
        """Pan using JavaScript injection as last resort"""
        try:
            # Try to pan using JavaScript scroll or transform
            if direction == 'right':
                script = f"window.scrollBy({distance_pixels}, 0);"
            elif direction == 'left':
                script = f"window.scrollBy({-distance_pixels}, 0);"
            elif direction == 'down':
                script = f"window.scrollBy(0, {distance_pixels});"
            elif direction == 'up':
                script = f"window.scrollBy(0, {-distance_pixels});"
            else:
                return False

            self.driver.execute_script(script)
            time.sleep(SCROLL_WAIT_TIME)
            print(f"Successfully panned {direction} using JavaScript")
            return True

        except Exception as e:
            print(f"JavaScript-based pan failed: {e}")
            return False
    
    def capture_map_screenshot(self):
        """Capture screenshot of the map area"""
        try:
            # Hide any overlays or UI elements that might interfere
            self.hide_ui_elements()
            
            # Wait a moment for UI to hide
            time.sleep(1)
            
            # Get map bounds
            map_bounds = self.get_map_bounds()
            
            # Take screenshot of the entire page
            screenshot_png = self.driver.get_screenshot_as_png()
            
            # Convert to PIL Image
            screenshot = Image.open(io.BytesIO(screenshot_png))
            
            # Crop to map area
            map_screenshot = screenshot.crop((
                map_bounds['x'],
                map_bounds['y'],
                map_bounds['x'] + map_bounds['width'],
                map_bounds['y'] + map_bounds['height']
            ))
            
            # Restore UI elements
            self.show_ui_elements()
            
            return map_screenshot
            
        except Exception as e:
            print(f"Screenshot capture failed: {e}")
            return None
    
    def hide_ui_elements(self):
        """Hide UI elements that might interfere with map capture"""
        try:
            # Common selectors for UI elements to hide
            ui_selectors = [
                ".menu", ".toolbar", ".controls", ".ui-panel",
                ".search-box", ".zoom-controls", ".attribution",
                "[class*='control']", "[class*='button']", "[class*='menu']"
            ]
            
            for selector in ui_selectors:
                self.driver.execute_script(f"""
                    var elements = document.querySelectorAll('{selector}');
                    for (var i = 0; i < elements.length; i++) {{
                        elements[i].style.visibility = 'hidden';
                    }}
                """)
                
        except Exception as e:
            print(f"Failed to hide UI elements: {e}")
    
    def show_ui_elements(self):
        """Restore hidden UI elements"""
        try:
            # Restore all hidden elements
            self.driver.execute_script("""
                var elements = document.querySelectorAll('*');
                for (var i = 0; i < elements.length; i++) {
                    if (elements[i].style.visibility === 'hidden') {
                        elements[i].style.visibility = 'visible';
                    }
                }
            """)
            
        except Exception as e:
            print(f"Failed to show UI elements: {e}")
    
    def close_browser(self):
        """Close the browser and clean up"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.map_loaded = False
        except Exception as e:
            print(f"Browser cleanup failed: {e}")


class WebMapCaptureApp:
    """Enhanced GUI application with web automation"""
    
    def __init__(self, root):
        self.root = root
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)
        self.root.resizable(False, False)
        
        # Keep window on top
        self.root.attributes('-topmost', KEEP_ON_TOP)
        
        # Initialize variables
        self.is_capturing = False
        self.web_controller = WebMapController()
        
        self.setup_gui()
    
    def setup_gui(self):
        """Create and configure the enhanced GUI elements"""
        # Main frame with scrollbar
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Web Map Capture Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Map URL configuration
        ttk.Label(main_frame, text="Map URL:", 
                 font=("Arial", 12, "bold")).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))
        self.url_var = tk.StringVar(value="https://hsac.org.in/eodb/")
        self.url_entry = ttk.Entry(main_frame, textvariable=self.url_var, width=50)
        self.url_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Grid configuration
        ttk.Label(main_frame, text="Grid Configuration:", 
                 font=("Arial", 12, "bold")).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(20, 10))
        
        # Rows input
        ttk.Label(main_frame, text="Number of Rows:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.rows_var = tk.StringVar(value=str(DEFAULT_ROWS))
        self.rows_entry = ttk.Entry(main_frame, textvariable=self.rows_var, width=10)
        self.rows_entry.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Columns input
        ttk.Label(main_frame, text="Number of Columns:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.cols_var = tk.StringVar(value=str(DEFAULT_COLS))
        self.cols_entry = ttk.Entry(main_frame, textvariable=self.cols_var, width=10)
        self.cols_entry.grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Browser control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=20)
        
        self.setup_button = ttk.Button(button_frame, text="Setup Browser", 
                                      command=self.setup_browser)
        self.setup_button.grid(row=0, column=0, padx=5)
        
        self.load_button = ttk.Button(button_frame, text="Load Map", 
                                     command=self.load_map, state='disabled')
        self.load_button.grid(row=0, column=1, padx=5)
        
        # Start capture button
        self.start_button = ttk.Button(main_frame, text="Start Capture", 
                                      command=self.start_capture_process, state='disabled')
        self.start_button.grid(row=7, column=0, columnspan=2, pady=20)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready - Click 'Setup Browser' to begin")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                     font=("Arial", 10), foreground="blue")
        self.status_label.grid(row=8, column=0, columnspan=2, pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='determinate')
        self.progress.grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_browser(self):
        """Setup the browser in a separate thread"""
        self.status_var.set("Setting up browser...")
        self.setup_button.config(state='disabled')

        def setup_thread():
            try:
                if self.web_controller.setup_browser():
                    self.status_var.set("Browser ready - Click 'Load Map'")
                    self.load_button.config(state='normal')
                else:
                    self.status_var.set("Browser setup failed")
                    self.setup_button.config(state='normal')
            except Exception as e:
                self.status_var.set(f"Browser setup error: {str(e)}")
                self.setup_button.config(state='normal')

        threading.Thread(target=setup_thread, daemon=True).start()

    def load_map(self):
        """Load the map in a separate thread"""
        self.status_var.set("Loading map...")
        self.load_button.config(state='disabled')

        def load_thread():
            try:
                # Update URL if changed
                self.web_controller.map_url = self.url_var.get()

                if self.web_controller.load_map():
                    self.status_var.set("Map loaded - Ready to capture")
                    self.start_button.config(state='normal')
                else:
                    self.status_var.set("Map loading failed")
                    self.load_button.config(state='normal')
            except Exception as e:
                self.status_var.set(f"Map loading error: {str(e)}")
                self.load_button.config(state='normal')

        threading.Thread(target=load_thread, daemon=True).start()

    def validate_inputs(self):
        """Validate user inputs"""
        try:
            rows = int(self.rows_var.get())
            cols = int(self.cols_var.get())

            if rows < 1 or cols < 1:
                raise ValueError("Rows and columns must be positive integers")

            if rows > 10 or cols > 10:
                raise ValueError("Maximum 10 rows and 10 columns allowed")

            return rows, cols

        except ValueError as e:
            messagebox.showerror("Input Error", str(e))
            return None, None

    def start_capture_process(self):
        """Start the capture process with validation and countdown"""
        if self.is_capturing:
            return

        rows, cols = self.validate_inputs()
        if rows is None:
            return

        if not self.web_controller.map_loaded:
            messagebox.showerror("Error", "Map not loaded. Please load the map first.")
            return

        self.is_capturing = True
        self.start_button.config(state='disabled')

        # Start countdown in a separate thread
        threading.Thread(target=self.countdown_and_capture,
                        args=(rows, cols), daemon=True).start()

    def countdown_and_capture(self, rows, cols):
        """Countdown and then start the capture process"""
        try:
            # Countdown
            for i in range(COUNTDOWN_TIME, 0, -1):
                self.status_var.set(f"Starting in {i}... Prepare the map view!")
                time.sleep(1)

            self.status_var.set("Starting capture process...")
            self.capture_screenshots(rows, cols)

        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            messagebox.showerror("Capture Error", f"An error occurred: {str(e)}")
        finally:
            self.is_capturing = False
            self.start_button.config(state='normal')
            self.progress['value'] = 0

    def capture_screenshots(self, rows, cols):
        """Main capture logic using web automation"""
        total_images = rows * cols
        current_image = 0

        self.progress['maximum'] = total_images

        # Create screenshots directory if it doesn't exist
        if not os.path.exists(SCREENSHOTS_DIR):
            os.makedirs(SCREENSHOTS_DIR)

        # Get map bounds for calculating pan distances
        map_bounds = self.web_controller.get_map_bounds()
        pan_distance_x = int(map_bounds['width'] * 0.8)  # 80% overlap
        pan_distance_y = int(map_bounds['height'] * 0.8)  # 80% overlap

        # Main capture loops
        for row in range(rows):
            for col in range(cols):
                current_image += 1
                self.status_var.set(f"Capturing image {current_image} of {total_images} (Row {row+1}, Col {col+1})")
                self.progress['value'] = current_image
                self.root.update()

                # Take screenshot
                screenshot = self.web_controller.capture_map_screenshot()
                if screenshot:
                    filename = f"{SCREENSHOTS_DIR}/capture_{row}_{col}.png"
                    screenshot.save(filename)
                else:
                    raise Exception(f"Failed to capture screenshot at row {row}, col {col}")

                # Horizontal pan (if not last column)
                if col < cols - 1:
                    self.status_var.set(f"Panning right... (Row {row+1}, Col {col+1} -> {col+2})")
                    self.root.update()

                    success = self.web_controller.pan_map('right', pan_distance_x)
                    if not success:
                        # Try alternative panning methods
                        print(f"Primary pan method failed, trying alternatives...")

                        # Try smaller distance
                        smaller_distance = pan_distance_x // 2
                        success = self.web_controller.pan_map('right', smaller_distance)

                        if not success:
                            # Try even smaller distance
                            tiny_distance = pan_distance_x // 4
                            success = self.web_controller.pan_map('right', tiny_distance)

                        if not success:
                            error_msg = f"Failed to pan right at row {row}, col {col}. All pan methods failed."
                            print(error_msg)
                            raise Exception(error_msg)

            # Vertical pan and horizontal reset (if not last row)
            if row < rows - 1:
                # Pan down
                if not self.web_controller.pan_map('down', pan_distance_y):
                    raise Exception(f"Failed to pan down at row {row}")

                # Reset horizontal position (pan back to left)
                for _ in range(cols - 1):
                    if not self.web_controller.pan_map('left', pan_distance_x):
                        raise Exception(f"Failed to reset horizontal position at row {row}")

        # Start stitching process
        self.status_var.set("Stitching images together...")
        self.root.update()
        self.stitch_images(rows, cols)

        self.status_var.set(f"Process completed! Check {FINAL_IMAGE_NAME}")
        messagebox.showinfo("Success", f"Map capture and stitching completed successfully!\nCheck '{FINAL_IMAGE_NAME}' in the current directory.")

    def stitch_images(self, rows, cols):
        """Stitch all captured images into one large image"""
        try:
            # Get dimensions from first image
            first_image = Image.open(f"{SCREENSHOTS_DIR}/capture_0_0.png")
            img_width, img_height = first_image.size
            first_image.close()

            # Create large canvas
            total_width = img_width * cols
            total_height = img_height * rows
            stitched_image = Image.new('RGB', (total_width, total_height))

            # Paste all images
            for row in range(rows):
                for col in range(cols):
                    filename = f"{SCREENSHOTS_DIR}/capture_{row}_{col}.png"
                    if os.path.exists(filename):
                        img = Image.open(filename)
                        paste_x = col * img_width
                        paste_y = row * img_height
                        stitched_image.paste(img, (paste_x, paste_y))
                        img.close()

            # Save final stitched image
            stitched_image.save(FINAL_IMAGE_NAME, quality=95)
            stitched_image.close()

        except Exception as e:
            raise Exception(f"Stitching failed: {str(e)}")

    def on_closing(self):
        """Handle application closing"""
        try:
            self.web_controller.close_browser()
        except:
            pass
        self.root.destroy()


def main():
    """Main function to run the application"""
    # Check if required modules are available
    try:
        from selenium import webdriver
        from PIL import Image
    except ImportError as e:
        print(f"Missing required module: {e}")
        print("Please install required packages:")
        print("pip install selenium webdriver-manager pillow")
        sys.exit(1)

    root = tk.Tk()
    app = WebMapCaptureApp(root)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)


if __name__ == "__main__":
    main()
