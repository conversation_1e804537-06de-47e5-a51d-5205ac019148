#!/usr/bin/env python3
"""
Simplified Web Map Capture Application

A streamlined version focused on solving the panning issue.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
import os
from PIL import Image

# Import selenium with error handling
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.chrome.options import Options
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError as e:
    print(f"Selenium not available: {e}")
    SELENIUM_AVAILABLE = False

class SimpleWebCapture:
    def __init__(self):
        self.driver = None
        self.map_url = "https://hsac.org.in/eodb/"
        
    def setup_browser(self):
        """Setup Chrome browser with robust settings"""
        if not SELENIUM_AVAILABLE:
            return False
            
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Remove automation detection
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
        except Exception as e:
            print(f"Browser setup failed: {e}")
            return False
    
    def load_map(self):
        """Load the map and wait for it to be ready"""
        try:
            print(f"Loading map: {self.map_url}")
            self.driver.get(self.map_url)
            
            # Wait for page to load
            time.sleep(8)  # Extended wait for HSAC
            
            print("Map loaded successfully")
            return True
        except Exception as e:
            print(f"Map loading failed: {e}")
            return False
    
    def simple_pan_right(self, distance=200):
        """Simple pan right using multiple methods"""
        print(f"Attempting to pan right by {distance} pixels...")
        
        # Method 1: JavaScript scroll
        try:
            self.driver.execute_script(f"window.scrollBy({distance}, 0);")
            time.sleep(2)
            print("✓ JavaScript scroll pan successful")
            return True
        except Exception as e:
            print(f"JavaScript scroll failed: {e}")
        
        # Method 2: Find and drag map element
        try:
            # Look for map elements
            map_element = None
            selectors = ["#map", "canvas", ".map-container"]
            
            for selector in selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    element = elements[0]
                    if element.size['width'] > 200 and element.size['height'] > 200:
                        map_element = element
                        print(f"Found map element: {selector}")
                        break
            
            if map_element:
                # Drag the map element
                actions = ActionChains(self.driver)
                actions.move_to_element(map_element)
                actions.click_and_hold()
                actions.move_by_offset(-distance, 0)  # Negative to pan right
                actions.release()
                actions.perform()
                
                time.sleep(2)
                print("✓ Element drag pan successful")
                return True
            else:
                print("No suitable map element found")
                
        except Exception as e:
            print(f"Element drag failed: {e}")
        
        # Method 3: Body drag
        try:
            body = self.driver.find_element(By.TAG_NAME, "body")
            actions = ActionChains(self.driver)
            actions.move_to_element(body)
            actions.move_by_offset(400, 300)  # Move to center area
            actions.click_and_hold()
            actions.move_by_offset(-distance, 0)
            actions.release()
            actions.perform()
            
            time.sleep(2)
            print("✓ Body drag pan successful")
            return True
            
        except Exception as e:
            print(f"Body drag failed: {e}")
        
        print("✗ All pan methods failed")
        return False
    
    def capture_screenshot(self):
        """Capture a screenshot of the map area"""
        try:
            import io

            # Take full page screenshot
            screenshot_png = self.driver.get_screenshot_as_png()

            # Convert to PIL Image
            screenshot = Image.open(io.BytesIO(screenshot_png))

            # For simplicity, return the full screenshot
            # In the full version, this would be cropped to map area
            return screenshot

        except Exception as e:
            print(f"Screenshot failed: {e}")
            return None
    
    def test_pan_sequence(self):
        """Test a sequence of pan operations"""
        print("\nTesting pan sequence...")
        
        if not self.driver:
            print("Browser not setup")
            return False
        
        # Test 3 pan operations
        for i in range(3):
            print(f"\nPan test {i+1}/3:")
            success = self.simple_pan_right(150)
            if not success:
                print(f"Pan test {i+1} failed")
                return False
            
            # Take a screenshot to verify pan worked
            screenshot = self.capture_screenshot()
            if screenshot:
                filename = f"test_pan_{i+1}.png"
                screenshot.save(filename)
                print(f"Screenshot saved: {filename}")
        
        print("\n✓ All pan tests successful!")
        return True
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()

class SimpleGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Simple Web Map Capture Test")
        self.root.geometry("400x300")
        
        self.web_capture = SimpleWebCapture()
        self.setup_gui()
    
    def setup_gui(self):
        """Setup the GUI"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        ttk.Label(main_frame, text="Simple Web Map Capture Test", 
                 font=("Arial", 14, "bold")).pack(pady=10)
        
        # Status
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.pack(pady=5)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="Setup Browser", 
                  command=self.setup_browser).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Load Map", 
                  command=self.load_map).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Test Pan", 
                  command=self.test_pan).pack(side=tk.LEFT, padx=5)
        
        # Instructions
        instructions = """
Instructions:
1. Click 'Setup Browser' to start Chrome
2. Click 'Load Map' to open HSAC map
3. Click 'Test Pan' to test panning

This will help identify which pan method works.
        """
        ttk.Label(main_frame, text=instructions, justify=tk.LEFT).pack(pady=20)
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_browser(self):
        """Setup browser in thread"""
        def setup():
            self.status_var.set("Setting up browser...")
            if self.web_capture.setup_browser():
                self.status_var.set("Browser ready")
            else:
                self.status_var.set("Browser setup failed")
        
        threading.Thread(target=setup, daemon=True).start()
    
    def load_map(self):
        """Load map in thread"""
        def load():
            self.status_var.set("Loading map...")
            if self.web_capture.load_map():
                self.status_var.set("Map loaded")
            else:
                self.status_var.set("Map loading failed")
        
        threading.Thread(target=load, daemon=True).start()
    
    def test_pan(self):
        """Test panning in thread"""
        def test():
            self.status_var.set("Testing pan operations...")
            if self.web_capture.test_pan_sequence():
                self.status_var.set("Pan test successful!")
                messagebox.showinfo("Success", "Pan test completed! Check the saved screenshots.")
            else:
                self.status_var.set("Pan test failed")
                messagebox.showerror("Error", "Pan test failed. Check console for details.")
        
        threading.Thread(target=test, daemon=True).start()
    
    def on_closing(self):
        """Handle window closing"""
        self.web_capture.close()
        self.root.destroy()
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

def main():
    """Main function"""
    if not SELENIUM_AVAILABLE:
        print("Selenium is not available. Please install it:")
        print("pip install selenium webdriver-manager")
        input("Press Enter to exit...")
        return
    
    print("Starting Simple Web Map Capture Test...")
    
    # Check if we should run GUI or command line
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--cli":
        # Command line mode
        capture = SimpleWebCapture()
        
        print("Setting up browser...")
        if not capture.setup_browser():
            print("Failed to setup browser")
            return
        
        print("Loading map...")
        if not capture.load_map():
            print("Failed to load map")
            capture.close()
            return
        
        print("Testing pan operations...")
        success = capture.test_pan_sequence()
        
        capture.close()
        
        if success:
            print("\n✓ Test completed successfully!")
        else:
            print("\n✗ Test failed")
    else:
        # GUI mode
        app = SimpleGUI()
        app.run()

if __name__ == "__main__":
    main()
