@echo off
echo Map Screenshot Capture Tool
echo ==========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.6 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install dependencies if needed
pip install -r requirements.txt

echo.
echo Starting Map Capture Application...
echo.

REM Run the application
python map_capture_app.py

echo.
echo Application closed.
pause
